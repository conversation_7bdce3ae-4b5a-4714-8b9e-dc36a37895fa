# نظام تسجيل الدخول الاحترافي

نظام تسجيل دخول احترافي باستخدام Python وFlask مع دعم كامل للغة العربية وتصميم عصري.

## المميزات

- ✅ واجهة عربية احترافية مع دعم RTL
- ✅ تصميم عصري باستخدام Bootstrap 5
- ✅ تشفير كلمات المرور باستخدام Werkzeug
- ✅ إدارة الجلسات الآمنة
- ✅ قاعدة بيانات SQLite مع SQLAlchemy
- ✅ رسائل تنبيه تفاعلية
- ✅ صفحات خطأ مخصصة
- ✅ تصميم متجاوب للشاشات المختلفة

## متطلبات النظام

- Python 3.7 أو أحدث
- pip (مدير حزم Python)

## التثبيت والتشغيل

### 1. تثبيت المتطلبات

```bash
pip install -r requirements.txt
```

### 2. إنشاء المستخدمين التجريبيين

```bash
python create_test_users.py
```

### 3. تشغيل التطبيق

```bash
python app.py
```

سيعمل التطبيق على العنوان: http://127.0.0.1:5000

## بيانات تسجيل الدخول التجريبية

| اسم المستخدم | كلمة المرور | الصلاحية |
|---------------|-------------|----------|
| admin         | admin123    | admin    |
| مدير          | 123456      | manager  |
| مستخدم        | user123     | user     |
| محمد          | mohammed123 | user     |
| فاطمة         | fatima123   | user     |

## هيكل المشروع

```
emsib/
├── app.py                 # التطبيق الرئيسي
├── models.py              # نماذج قاعدة البيانات
├── create_test_users.py   # سكريبت إنشاء المستخدمين التجريبيين
├── requirements.txt       # متطلبات المشروع
├── README.md             # ملف التوثيق
├── templates/            # قوالب HTML
│   ├── base.html         # القالب الأساسي
│   ├── login.html        # صفحة تسجيل الدخول
│   ├── dashboard.html    # الصفحة الرئيسية
│   ├── profile.html      # الملف الشخصي
│   └── error.html        # صفحة الأخطاء
└── static/               # الملفات الثابتة
    └── css/
        └── style.css     # ملف التنسيق المخصص
```

## الصفحات المتاحة

- `/` - الصفحة الرئيسية (إعادة توجيه)
- `/login` - صفحة تسجيل الدخول
- `/dashboard` - الصفحة الرئيسية بعد تسجيل الدخول
- `/profile` - الملف الشخصي
- `/logout` - تسجيل الخروج

## الأمان

- تشفير كلمات المرور باستخدام `werkzeug.security`
- إدارة الجلسات الآمنة مع انتهاء صلاحية
- حماية الصفحات المحمية من الوصول غير المصرح
- التحقق من صحة البيانات المدخلة

## التخصيص

### تغيير المفتاح السري

في ملف `app.py`، قم بتغيير:

```python
app.config['SECRET_KEY'] = 'your-secret-key-change-this-in-production'
```

### إضافة مستخدمين جدد

```bash
python create_test_users.py create
```

### عرض جميع المستخدمين

```bash
python create_test_users.py list
```

### حذف جميع المستخدمين

```bash
python create_test_users.py delete
```

## التطوير

### إضافة صفحات جديدة

1. أنشئ route جديد في `app.py`
2. أنشئ قالب HTML في مجلد `templates`
3. أضف التنسيق المطلوب في `static/css/style.css`

### إضافة حقول جديدة للمستخدم

1. عدّل نموذج `User` في `models.py`
2. احذف ملف قاعدة البيانات `login_system.db`
3. شغّل التطبيق مرة أخرى لإنشاء الجداول الجديدة

## استكشاف الأخطاء

### خطأ في قاعدة البيانات

```bash
# احذف ملف قاعدة البيانات وأعد إنشاءه
rm login_system.db
python app.py
```

### خطأ في المتطلبات

```bash
# تأكد من تثبيت جميع المتطلبات
pip install -r requirements.txt --upgrade
```

## الدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى إنشاء issue في المستودع.

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.
