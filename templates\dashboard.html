{% extends "base.html" %}

{% block title %}الصفحة الرئيسية - نظام الإدارة{% endblock %}

{% block content %}
<div class="dashboard-container">
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-tachometer-alt me-2"></i>
                نظام الإدارة
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ url_for('dashboard') }}">
                            <i class="fas fa-home me-1"></i>
                            الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('profile') }}">
                            <i class="fas fa-user me-1"></i>
                            الملف الشخصي
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>
                            {{ session.username }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li>
                                <a class="dropdown-item" href="{{ url_for('profile') }}">
                                    <i class="fas fa-user me-2"></i>
                                    الملف الشخصي
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('logout') }}">
                                    <i class="fas fa-sign-out-alt me-2"></i>
                                    تسجيل الخروج
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- المحتوى الرئيسي -->
    <div class="dashboard-content">
        <div class="container">
            <!-- بطاقة الترحيب -->
            <div class="welcome-card">
                <div class="welcome-icon">
                    <i class="fas fa-user-check"></i>
                </div>
                <h2 class="mb-3">مرحباً بك، {{ user.username }}!</h2>
                <p class="text-muted mb-4">
                    تم تسجيل دخولك بنجاح إلى نظام الإدارة. يمكنك الآن الوصول إلى جميع الخدمات المتاحة.
                </p>
                <div class="row text-center">
                    <div class="col-md-4">
                        <small class="text-muted d-block">الصلاحية</small>
                        <strong class="text-primary">{{ user.role|title }}</strong>
                    </div>
                    <div class="col-md-4">
                        <small class="text-muted d-block">تاريخ الانضمام</small>
                        <strong class="text-success">{{ user.created_at.strftime('%Y/%m/%d') if user.created_at else 'غير محدد' }}</strong>
                    </div>
                    <div class="col-md-4">
                        <small class="text-muted d-block">آخر تسجيل دخول</small>
                        <strong class="text-info">{{ user.last_login.strftime('%Y/%m/%d %H:%M') if user.last_login else 'المرة الأولى' }}</strong>
                    </div>
                </div>
            </div>
            
            <!-- إحصائيات سريعة -->
            <div class="row">
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stats-card">
                        <div class="stats-icon primary">
                            <i class="fas fa-users"></i>
                        </div>
                        <h4 class="mb-2">المستخدمون</h4>
                        <h2 class="text-primary mb-0">1</h2>
                        <small class="text-muted">مستخدم نشط</small>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stats-card">
                        <div class="stats-icon success">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <h4 class="mb-2">العمليات</h4>
                        <h2 class="text-success mb-0">100%</h2>
                        <small class="text-muted">معدل النجاح</small>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stats-card">
                        <div class="stats-icon info">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h4 class="mb-2">الأمان</h4>
                        <h2 class="text-info mb-0">عالي</h2>
                        <small class="text-muted">مستوى الحماية</small>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stats-card">
                        <div class="stats-icon warning">
                            <i class="fas fa-clock"></i>
                        </div>
                        <h4 class="mb-2">وقت التشغيل</h4>
                        <h2 class="text-warning mb-0">24/7</h2>
                        <small class="text-muted">متاح دائماً</small>
                    </div>
                </div>
            </div>
            
            <!-- روابط سريعة -->
            <div class="row">
                <div class="col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-user-cog fa-3x text-primary mb-3"></i>
                            <h5 class="card-title">إدارة الملف الشخصي</h5>
                            <p class="card-text text-muted">
                                عرض وتحديث معلومات الملف الشخصي والإعدادات
                            </p>
                            <a href="{{ url_for('profile') }}" class="btn btn-outline-primary">
                                <i class="fas fa-arrow-left me-2"></i>
                                الانتقال للملف الشخصي
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-cogs fa-3x text-success mb-3"></i>
                            <h5 class="card-title">إعدادات النظام</h5>
                            <p class="card-text text-muted">
                                تخصيص إعدادات النظام والتفضيلات الشخصية
                            </p>
                            <button class="btn btn-outline-success" onclick="showComingSoon()">
                                <i class="fas fa-arrow-left me-2"></i>
                                قريباً
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function showComingSoon() {
    alert('هذه الميزة ستكون متاحة قريباً!');
}

// تحديث الوقت كل ثانية
function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleString('ar-SA', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
    
    // يمكن إضافة عنصر لعرض الوقت إذا أردت
    console.log('الوقت الحالي:', timeString);
}

// تشغيل تحديث الوقت كل ثانية
setInterval(updateTime, 1000);

// رسالة ترحيب عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('مرحباً بك في نظام الإدارة!');
});
</script>
{% endblock %}
