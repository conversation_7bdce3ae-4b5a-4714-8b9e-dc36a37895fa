#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت إنشاء مستخدمين تجريبيين لاختبار نظام تسجيل الدخول
"""

from app import app
from models import db, User

def create_test_users():
    """إنشاء مستخدمين تجريبيين"""
    
    with app.app_context():
        # إنشاء جداول قاعدة البيانات إذا لم تكن موجودة
        db.create_all()
        
        # قائمة المستخدمين التجريبيين
        test_users = [
            {
                'username': 'admin',
                'password': 'admin123',
                'role': 'admin'
            },
            {
                'username': 'مدير',
                'password': '123456',
                'role': 'manager'
            },
            {
                'username': 'مستخدم',
                'password': 'user123',
                'role': 'user'
            },
            {
                'username': 'محمد',
                'password': 'mohammed123',
                'role': 'user'
            },
            {
                'username': 'فاطمة',
                'password': 'fatima123',
                'role': 'user'
            }
        ]
        
        print("بدء إنشاء المستخدمين التجريبيين...")
        print("-" * 50)
        
        created_count = 0
        existing_count = 0
        
        for user_data in test_users:
            # التحقق من وجود المستخدم
            existing_user = User.query.filter_by(username=user_data['username']).first()
            
            if existing_user:
                print(f"❌ المستخدم '{user_data['username']}' موجود بالفعل")
                existing_count += 1
            else:
                # إنشاء مستخدم جديد
                new_user = User(
                    username=user_data['username'],
                    password=user_data['password'],
                    role=user_data['role']
                )
                
                try:
                    db.session.add(new_user)
                    db.session.commit()
                    print(f"✅ تم إنشاء المستخدم '{user_data['username']}' بنجاح")
                    print(f"   - الصلاحية: {user_data['role']}")
                    print(f"   - كلمة المرور: {user_data['password']}")
                    created_count += 1
                except Exception as e:
                    db.session.rollback()
                    print(f"❌ خطأ في إنشاء المستخدم '{user_data['username']}': {str(e)}")
        
        print("-" * 50)
        print(f"تم إنشاء {created_count} مستخدم جديد")
        print(f"يوجد {existing_count} مستخدم موجود مسبقاً")
        print(f"إجمالي المستخدمين في النظام: {User.query.count()}")
        
        if created_count > 0:
            print("\n" + "=" * 50)
            print("بيانات تسجيل الدخول للمستخدمين الجدد:")
            print("=" * 50)
            
            for user_data in test_users:
                if not User.query.filter_by(username=user_data['username']).first() or created_count > 0:
                    print(f"اسم المستخدم: {user_data['username']}")
                    print(f"كلمة المرور: {user_data['password']}")
                    print(f"الصلاحية: {user_data['role']}")
                    print("-" * 30)

def list_all_users():
    """عرض جميع المستخدمين في النظام"""
    
    with app.app_context():
        users = User.query.all()
        
        if not users:
            print("لا يوجد مستخدمين في النظام")
            return
        
        print("\n" + "=" * 60)
        print("جميع المستخدمين في النظام:")
        print("=" * 60)
        
        for user in users:
            print(f"ID: {user.id}")
            print(f"اسم المستخدم: {user.username}")
            print(f"الصلاحية: {user.role}")
            print(f"تاريخ الإنشاء: {user.created_at.strftime('%Y-%m-%d %H:%M:%S') if user.created_at else 'غير محدد'}")
            print(f"آخر تسجيل دخول: {user.last_login.strftime('%Y-%m-%d %H:%M:%S') if user.last_login else 'لم يسجل دخول بعد'}")
            print(f"الحالة: {'نشط' if user.is_active else 'غير نشط'}")
            print("-" * 60)

def delete_all_users():
    """حذف جميع المستخدمين (للاختبار فقط)"""
    
    with app.app_context():
        confirm = input("هل أنت متأكد من حذف جميع المستخدمين؟ (اكتب 'نعم' للتأكيد): ")
        
        if confirm.lower() in ['نعم', 'yes', 'y']:
            try:
                count = User.query.count()
                User.query.delete()
                db.session.commit()
                print(f"تم حذف {count} مستخدم بنجاح")
            except Exception as e:
                db.session.rollback()
                print(f"خطأ في حذف المستخدمين: {str(e)}")
        else:
            print("تم إلغاء العملية")

if __name__ == '__main__':
    import sys
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == 'create':
            create_test_users()
        elif command == 'list':
            list_all_users()
        elif command == 'delete':
            delete_all_users()
        else:
            print("الأوامر المتاحة:")
            print("python create_test_users.py create  - إنشاء مستخدمين تجريبيين")
            print("python create_test_users.py list    - عرض جميع المستخدمين")
            print("python create_test_users.py delete  - حذف جميع المستخدمين")
    else:
        # تشغيل إنشاء المستخدمين افتراضياً
        create_test_users()
