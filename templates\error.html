{% extends "base.html" %}

{% block title %}خطأ {{ error_code }} - نظام الإدارة{% endblock %}

{% block content %}
<div class="login-container">
    <div class="login-card">
        <div class="login-header">
            <div class="logo-container">
                <div class="logo">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h1 class="login-title">خطأ {{ error_code }}</h1>
                <p class="login-subtitle">{{ error_message }}</p>
            </div>
        </div>
        
        <div class="login-body text-center">
            {% if error_code == 404 %}
                <p class="mb-4">الصفحة التي تبحث عنها غير موجودة أو تم نقلها إلى موقع آخر.</p>
            {% elif error_code == 500 %}
                <p class="mb-4">حدث خطأ في الخادم. يرجى المحاولة مرة أخرى لاحقاً.</p>
            {% else %}
                <p class="mb-4">حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.</p>
            {% endif %}
            
            <div class="d-grid gap-2">
                {% if session.user_id %}
                    <a href="{{ url_for('dashboard') }}" class="btn btn-login">
                        <i class="fas fa-home me-2"></i>
                        العودة للصفحة الرئيسية
                    </a>
                {% else %}
                    <a href="{{ url_for('login') }}" class="btn btn-login">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        تسجيل الدخول
                    </a>
                {% endif %}
                
                <button onclick="history.back()" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للصفحة السابقة
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}
