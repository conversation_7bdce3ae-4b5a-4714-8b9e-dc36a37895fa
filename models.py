from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime

db = SQLAlchemy()

class User(db.Model):
    """نموذج المستخدم في قاعدة البيانات"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    role = db.Column(db.String(50), nullable=False, default='user')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)
    is_active = db.Column(db.<PERSON>, default=True)
    
    def __init__(self, username, password, role='user'):
        """إنشاء مستخدم جديد"""
        self.username = username
        self.set_password(password)
        self.role = role
    
    def set_password(self, password):
        """تشفير وحفظ كلمة المرور"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """التحقق من كلمة المرور"""
        return check_password_hash(self.password_hash, password)
    
    def update_last_login(self):
        """تحديث وقت آخر تسجيل دخول"""
        self.last_login = datetime.utcnow()
        db.session.commit()
    
    def __repr__(self):
        return f'<User {self.username}>'
    
    @staticmethod
    def authenticate(username, password):
        """التحقق من بيانات تسجيل الدخول"""
        user = User.query.filter_by(username=username, is_active=True).first()
        if user and user.check_password(password):
            user.update_last_login()
            return user
        return None
    
    def to_dict(self):
        """تحويل بيانات المستخدم إلى قاموس"""
        return {
            'id': self.id,
            'username': self.username,
            'role': self.role,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None,
            'is_active': self.is_active
        }
