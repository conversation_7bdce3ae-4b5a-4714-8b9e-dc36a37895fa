from flask import Flask, render_template, request, redirect, url_for, session, flash
from models import db, User
import os
from datetime import timedelta

# إنشاء تطبيق Flask
app = Flask(__name__)

# إعدادات التطبيق
app.config['SECRET_KEY'] = 'your-secret-key-change-this-in-production'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///login_system.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(hours=2)

# تهيئة قاعدة البيانات
db.init_app(app)

def login_required(f):
    """ديكوريتر للتحقق من تسجيل الدخول"""
    from functools import wraps
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('يجب تسجيل الدخول أولاً للوصول إلى هذه الصفحة', 'warning')
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

@app.route('/')
def index():
    """الصفحة الرئيسية - إعادة توجيه حسب حالة تسجيل الدخول"""
    if 'user_id' in session:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    """صفحة تسجيل الدخول"""
    if 'user_id' in session:
        return redirect(url_for('dashboard'))
    
    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '')
        
        # التحقق من وجود البيانات
        if not username or not password:
            flash('يرجى إدخال اسم المستخدم وكلمة المرور', 'error')
            return render_template('login.html')
        
        # التحقق من بيانات المستخدم
        user = User.authenticate(username, password)
        
        if user:
            # تسجيل دخول ناجح
            session.permanent = True
            session['user_id'] = user.id
            session['username'] = user.username
            session['role'] = user.role
            
            flash(f'مرحباً بك {user.username}!', 'success')
            return redirect(url_for('dashboard'))
        else:
            # فشل في تسجيل الدخول
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template('login.html')

@app.route('/dashboard')
@login_required
def dashboard():
    """الصفحة الرئيسية بعد تسجيل الدخول"""
    user = User.query.get(session['user_id'])
    return render_template('dashboard.html', user=user)

@app.route('/logout')
@login_required
def logout():
    """تسجيل الخروج"""
    username = session.get('username', 'المستخدم')
    session.clear()
    flash(f'تم تسجيل الخروج بنجاح. وداعاً {username}!', 'info')
    return redirect(url_for('login'))

@app.route('/profile')
@login_required
def profile():
    """صفحة الملف الشخصي"""
    user = User.query.get(session['user_id'])
    return render_template('profile.html', user=user)

@app.errorhandler(404)
def not_found(error):
    """صفحة الخطأ 404"""
    return render_template('error.html', 
                         error_code=404, 
                         error_message='الصفحة المطلوبة غير موجودة'), 404

@app.errorhandler(500)
def internal_error(error):
    """صفحة الخطأ 500"""
    db.session.rollback()
    return render_template('error.html', 
                         error_code=500, 
                         error_message='حدث خطأ داخلي في الخادم'), 500

def create_tables():
    """إنشاء جداول قاعدة البيانات"""
    with app.app_context():
        db.create_all()
        print("تم إنشاء جداول قاعدة البيانات بنجاح")

if __name__ == '__main__':
    # إنشاء جداول قاعدة البيانات
    create_tables()
    
    # تشغيل التطبيق
    print("تشغيل تطبيق تسجيل الدخول...")
    print("يمكنك الوصول إلى التطبيق عبر: http://127.0.0.1:5000")
    app.run(debug=True, host='127.0.0.1', port=5000)
