{% extends "base.html" %}

{% block title %}تسجيل الدخول - نظام الإدارة{% endblock %}

{% block content %}
<div class="login-container">
    <div class="login-card">
        <!-- رأس صفحة تسجيل الدخول -->
        <div class="login-header">
            <div class="logo-container">
                <div class="logo">
                    <i class="fas fa-user-shield"></i>
                </div>
                <h1 class="login-title">نظام الإدارة</h1>
                <p class="login-subtitle">مرحباً بك، يرجى تسجيل الدخول للمتابعة</p>
            </div>
        </div>
        
        <!-- جسم صفحة تسجيل الدخول -->
        <div class="login-body">
            <form method="POST" action="{{ url_for('login') }}" id="loginForm">
                <!-- حقل اسم المستخدم -->
                <div class="form-group">
                    <label for="username" class="form-label">
                        <i class="fas fa-user me-2"></i>
                        اسم المستخدم
                    </label>
                    <div class="input-group">
                        <input type="text" 
                               class="form-control" 
                               id="username" 
                               name="username" 
                               placeholder="أدخل اسم المستخدم"
                               required
                               autocomplete="username"
                               value="{{ request.form.username if request.form.username }}">
                        <span class="input-group-text">
                            <i class="fas fa-user"></i>
                        </span>
                    </div>
                </div>
                
                <!-- حقل كلمة المرور -->
                <div class="form-group">
                    <label for="password" class="form-label">
                        <i class="fas fa-lock me-2"></i>
                        كلمة المرور
                    </label>
                    <div class="input-group">
                        <input type="password" 
                               class="form-control" 
                               id="password" 
                               name="password" 
                               placeholder="أدخل كلمة المرور"
                               required
                               autocomplete="current-password">
                        <span class="input-group-text">
                            <i class="fas fa-lock"></i>
                        </span>
                    </div>
                </div>
                
                <!-- زر تسجيل الدخول -->
                <div class="form-group">
                    <button type="submit" class="btn btn-login">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        تسجيل الدخول
                    </button>
                </div>
            </form>
            
            <!-- معلومات إضافية -->
            <div class="text-center mt-4">
                <small class="text-muted">
                    <i class="fas fa-shield-alt me-1"></i>
                    جميع البيانات محمية ومشفرة
                </small>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('loginForm');
    const usernameInput = document.getElementById('username');
    const passwordInput = document.getElementById('password');
    const submitButton = form.querySelector('button[type="submit"]');
    
    // التحقق من صحة النموذج
    function validateForm() {
        const username = usernameInput.value.trim();
        const password = passwordInput.value;
        
        if (username.length < 3) {
            showFieldError(usernameInput, 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل');
            return false;
        }
        
        if (password.length < 4) {
            showFieldError(passwordInput, 'كلمة المرور يجب أن تكون 4 أحرف على الأقل');
            return false;
        }
        
        clearFieldErrors();
        return true;
    }
    
    // عرض خطأ في الحقل
    function showFieldError(input, message) {
        clearFieldErrors();
        input.classList.add('is-invalid');
        
        const errorDiv = document.createElement('div');
        errorDiv.className = 'invalid-feedback';
        errorDiv.textContent = message;
        input.parentNode.appendChild(errorDiv);
    }
    
    // مسح أخطاء الحقول
    function clearFieldErrors() {
        const inputs = form.querySelectorAll('.form-control');
        inputs.forEach(input => {
            input.classList.remove('is-invalid');
        });
        
        const errorMessages = form.querySelectorAll('.invalid-feedback');
        errorMessages.forEach(error => error.remove());
    }
    
    // التحقق عند الإرسال
    form.addEventListener('submit', function(e) {
        if (!validateForm()) {
            e.preventDefault();
            return false;
        }
        
        // تغيير نص الزر أثناء الإرسال
        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري تسجيل الدخول...';
        submitButton.disabled = true;
    });
    
    // مسح الأخطاء عند الكتابة
    [usernameInput, passwordInput].forEach(input => {
        input.addEventListener('input', clearFieldErrors);
    });
    
    // التركيز على أول حقل فارغ
    if (!usernameInput.value) {
        usernameInput.focus();
    } else if (!passwordInput.value) {
        passwordInput.focus();
    }
    
    // دعم Enter للانتقال بين الحقول
    usernameInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            passwordInput.focus();
        }
    });
});
</script>
{% endblock %}
