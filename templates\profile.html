{% extends "base.html" %}

{% block title %}الملف الشخصي - نظام الإدارة{% endblock %}

{% block content %}
<div class="dashboard-container">
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-tachometer-alt me-2"></i>
                نظام الإدارة
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('dashboard') }}">
                            <i class="fas fa-home me-1"></i>
                            الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ url_for('profile') }}">
                            <i class="fas fa-user me-1"></i>
                            الملف الشخصي
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>
                            {{ session.username }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li>
                                <a class="dropdown-item" href="{{ url_for('profile') }}">
                                    <i class="fas fa-user me-2"></i>
                                    الملف الشخصي
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('logout') }}">
                                    <i class="fas fa-sign-out-alt me-2"></i>
                                    تسجيل الخروج
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- المحتوى الرئيسي -->
    <div class="dashboard-content">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <!-- بطاقة الملف الشخصي -->
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h4 class="mb-0">
                                <i class="fas fa-user me-2"></i>
                                الملف الشخصي
                            </h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4 text-center mb-4">
                                    <div class="profile-avatar">
                                        <i class="fas fa-user-circle fa-8x text-primary"></i>
                                    </div>
                                    <h5 class="mt-3">{{ user.username }}</h5>
                                    <span class="badge bg-primary">{{ user.role|title }}</span>
                                </div>
                                <div class="col-md-8">
                                    <h5 class="mb-3">معلومات الحساب</h5>
                                    
                                    <div class="row mb-3">
                                        <div class="col-sm-4">
                                            <strong>اسم المستخدم:</strong>
                                        </div>
                                        <div class="col-sm-8">
                                            {{ user.username }}
                                        </div>
                                    </div>
                                    
                                    <div class="row mb-3">
                                        <div class="col-sm-4">
                                            <strong>الصلاحية:</strong>
                                        </div>
                                        <div class="col-sm-8">
                                            <span class="badge bg-success">{{ user.role|title }}</span>
                                        </div>
                                    </div>
                                    
                                    <div class="row mb-3">
                                        <div class="col-sm-4">
                                            <strong>تاريخ الانضمام:</strong>
                                        </div>
                                        <div class="col-sm-8">
                                            {{ user.created_at.strftime('%Y/%m/%d %H:%M') if user.created_at else 'غير محدد' }}
                                        </div>
                                    </div>
                                    
                                    <div class="row mb-3">
                                        <div class="col-sm-4">
                                            <strong>آخر تسجيل دخول:</strong>
                                        </div>
                                        <div class="col-sm-8">
                                            {{ user.last_login.strftime('%Y/%m/%d %H:%M') if user.last_login else 'المرة الأولى' }}
                                        </div>
                                    </div>
                                    
                                    <div class="row mb-3">
                                        <div class="col-sm-4">
                                            <strong>حالة الحساب:</strong>
                                        </div>
                                        <div class="col-sm-8">
                                            {% if user.is_active %}
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check me-1"></i>
                                                    نشط
                                                </span>
                                            {% else %}
                                                <span class="badge bg-danger">
                                                    <i class="fas fa-times me-1"></i>
                                                    غير نشط
                                                </span>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- إحصائيات الحساب -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-bar me-2"></i>
                                إحصائيات الحساب
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-md-3 mb-3">
                                    <div class="stats-item">
                                        <i class="fas fa-calendar-alt fa-2x text-primary mb-2"></i>
                                        <h6>أيام العضوية</h6>
                                        <h4 class="text-primary">
                                            {% if user.created_at %}
                                                {{ (moment().date() - user.created_at.date()).days + 1 if moment else 'N/A' }}
                                            {% else %}
                                                1
                                            {% endif %}
                                        </h4>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <div class="stats-item">
                                        <i class="fas fa-sign-in-alt fa-2x text-success mb-2"></i>
                                        <h6>مرات تسجيل الدخول</h6>
                                        <h4 class="text-success">1+</h4>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <div class="stats-item">
                                        <i class="fas fa-shield-alt fa-2x text-info mb-2"></i>
                                        <h6>مستوى الأمان</h6>
                                        <h4 class="text-info">عالي</h4>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <div class="stats-item">
                                        <i class="fas fa-star fa-2x text-warning mb-2"></i>
                                        <h6>التقييم</h6>
                                        <h4 class="text-warning">ممتاز</h4>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- أزرار الإجراءات -->
                    <div class="card mt-4">
                        <div class="card-body text-center">
                            <h5 class="mb-3">إجراءات الحساب</h5>
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-primary" onclick="showComingSoon()">
                                    <i class="fas fa-edit me-2"></i>
                                    تحديث المعلومات
                                </button>
                                <button type="button" class="btn btn-outline-warning" onclick="showComingSoon()">
                                    <i class="fas fa-key me-2"></i>
                                    تغيير كلمة المرور
                                </button>
                                <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-right me-2"></i>
                                    العودة للرئيسية
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function showComingSoon() {
    alert('هذه الميزة ستكون متاحة قريباً!');
}
</script>
{% endblock %}
